'use client'

import { usePathname } from 'next/navigation'
import VesselDropdown from './components/vessel-dropdown'
import TrainingTypeDropdown from './components/training-type-dropdown'
import CrewDropdown from './components/crew-dropdown/crew-dropdown'
import DateRange from '../DateRange'
import CrewDutyDropdown from './components/crew-duty-dropdown'
import TrainingStatusDropdown from './components/training-status-dropdown'
import { debounce } from 'lodash'
import SupplierDropdown from './components/supplier-dropdown'
import CategoryDropdown from './components/category-dropdown'
import { use, useEffect, useState, useCallback } from 'react'
import { Input } from '@/components/ui/input'
import { Combobox } from '@/components/ui/comboBox'
import MaintenanceCategoryDropdown from './components/maintenance-category-dropdown'

import { CrewTrainingFilterActions } from './components/training-actions'
import { Label } from '@/components/ui/label'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import TimeField from '@/app/ui/logbook/components/time'
import dayjs from 'dayjs'
import LocationField from '@/app/ui/logbook/components/location/location'
import type { DateRange as TDateRange } from 'react-day-picker'
import { Button } from '@/components/ui/button'
import { CheckIcon } from 'lucide-react'
import SeaLogsButton from '../ui/sea-logs-button'
import { Card, CardContent } from '../ui'
import { cn } from '@/app/lib/utils'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion'
import { useBreakpoints } from '../hooks/useBreakpoints'

const Filter = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
    supplierIdOptions = [],
    categoryIdOptions = [],
    onClick,
    crewData,
    vesselData,
    tripReportFilterData = {},
    table,
}: any) => {
    const pathname = usePathname()
    const [selectedOptions, setSelectedOptions] = useState({
        vessel: null,
        supplier: null,
        category: null,
    })
    const [filteredOptions, setFilteredOptions] = useState({
        vesselIdOptions,
        supplierIdOptions,
        categoryIdOptions,
    })

    const handleOnChange = ({ type, data }: any) => {
        const newSelectedOptions = { ...selectedOptions, [type]: data }
        setSelectedOptions(newSelectedOptions)

        filterOptions(newSelectedOptions)

        onChange({ type, data })
    }

    const filterOptions = (selectedOptions: any) => {
        let newSupplierIdOptions = supplierIdOptions
        let newCategoryIdOptions = categoryIdOptions

        if (selectedOptions.vessel) {
            newSupplierIdOptions = supplierIdOptions.filter((supplier: any) => {
                return supplier.vesselId === selectedOptions.vessel.id
            })
        }

        if (selectedOptions.supplier) {
            newCategoryIdOptions = categoryIdOptions.filter((category: any) => {
                return category.supplierId === selectedOptions.supplier.id
            })
        }

        setFilteredOptions({
            vesselIdOptions: vesselIdOptions,
            supplierIdOptions: newSupplierIdOptions,
            categoryIdOptions: newCategoryIdOptions,
        })
    }

    const handleOnClick = () => {
        onClick()
    }
    return (
        <div>
            {/* <Label>Filter</Label> */}
            <div>
                {pathname === '/vessel' && (
                    <VesselListFilter table={table} onChange={handleOnChange} />
                )}
                {pathname === '/crew-training' && (
                    <UnifiedTrainingListFilter
                        onChange={handleOnChange}
                        vesselIdOptions={vesselIdOptions}
                        trainingTypeIdOptions={trainingTypeIdOptions}
                        memberId={memberId}
                        trainerIdOptions={trainerIdOptions}
                        memberIdOptions={memberIdOptions}
                    />
                )}
                {pathname === '/crew/info' && (
                    <AllocatedTasksFilter onChange={handleOnChange} />
                )}
                {pathname === '/crew' && (
                    <CrewListFilter onChange={handleOnChange} />
                )}
                {pathname === '/inventory' && (
                    <InventoryListFilter
                        onChange={handleOnChange}
                        vesselIdOptions={filteredOptions.vesselIdOptions}
                        supplierIdOptions={filteredOptions.supplierIdOptions}
                        categoryIdOptions={filteredOptions.categoryIdOptions}
                    />
                )}
                {pathname === '/inventory/suppliers' && (
                    <SupplierListFilter onChange={handleOnChange} />
                )}
                {pathname === '/key-contacts' && (
                    <SearchInputOnlyFilter onChange={handleOnChange} />
                )}
                {pathname === '/maintenance' && (
                    <MaintenanceListFilter onChange={handleOnChange} />
                )}
                {pathname === '/incident-records' && (
                    <IncidentRecordFilter onChange={handleOnChange} />
                )}
                {pathname === '/training-type' && (
                    <TrainingTypeListFilter onChange={handleOnChange} />
                )}
                {pathname === '/reporting' && (
                    <ReporingFilters
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                        crewData={crewData}
                        vesselData={vesselData}
                    />
                )}
                {pathname === '/reporting/crew-seatime-report' && (
                    <CrewSeatimeReportFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/crew-training-completed-report' && (
                    <TrainingCompletedReportFilter
                        onChange={handleOnChange}
                        vesselIdOptions={vesselIdOptions}
                        trainingTypeIdOptions={trainingTypeIdOptions}
                        memberId={memberId}
                        trainerIdOptions={trainerIdOptions}
                        memberIdOptions={memberIdOptions}
                    />
                )}
                {pathname === '/reporting/simple-fuel-report' && (
                    <MultiVesselsDateRangeFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/engine-hours-report' && (
                    <MultiVesselsDateRangeFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/service-report' && (
                    <MultiVesselsDateRangeFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {pathname === '/reporting/activity-reports' && (
                    <ActivityReportFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {(pathname === '/reporting/maintenance-status-activity' ||
                    pathname === '/reporting/maintenance-cost-track') && (
                    <MaintenanceReportFilter
                        onChange={handleOnChange}
                        onClickButton={handleOnClick}
                    />
                )}
                {(pathname === '/reporting/fuel-analysis' ||
                    pathname === '/reporting/fuel-tasking-analysis' ||
                    pathname === '/reporting/detailed-fuel-report' ||
                    pathname === '/reporting/fuel-summary-report') && (
                    <FuelReporingFilters onChange={handleOnChange} />
                )}
                {pathname === '/document-locker' && (
                    <DocumentLockerFilter onChange={handleOnChange} />
                )}
                {pathname === '/calendar' && (
                    <CalendarFilter onChange={handleOnChange} />
                )}
                {pathname === '/reporting/trip-report' && (
                    <TripReportFilters
                        tripReportFilterData={tripReportFilterData}
                        onChange={handleOnChange}
                        onClick={onClick}
                    />
                )}
            </div>
        </div>
    )
}

export default Filter

const VesselListFilter = ({ onChange, table }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-1 items-center justify-between">
            <Input
                type="search"
                placeholder="Search"
                value={
                    (table.getAllColumns()?.[0]?.getFilterValue() as string) ??
                    ''
                }
                onChange={(event: any) =>
                    table
                        .getAllColumns()?.[0]
                        ?.setFilterValue(event.target.value)
                }
                className="h-11 w-[150px] lg:w-[250px]"
            />
        </div>
    )
}

export const TrainingListFilter = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
    overdueSwitcher = false,
    excludeFilters = [],
}: any) => {
    const [overdueList, setOverdueList] = useState(overdueSwitcher)

    useEffect(() => {
        setOverdueList(overdueSwitcher)
    }, [overdueSwitcher])

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const bp = useBreakpoints()

    const filterContent = (
        <div className="grid xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2.5">
            {!overdueList !== true && !excludeFilters.includes('dateRange') && (
                <div>
                    <DateRange
                        onChange={(data: any) =>
                            handleDropdownChange('dateRange', data)
                        }
                        clearable
                    />
                </div>
            )}

            {!excludeFilters.includes('trainingType') && (
                <div>
                    <TrainingTypeDropdown
                        isClearable={true}
                        onChange={(data: any) =>
                            handleDropdownChange('trainingType', data)
                        }
                        trainingTypeIdOptions={trainingTypeIdOptions}
                    />
                </div>
            )}

            {!excludeFilters.includes('vessel') && (
                <div>
                    <VesselDropdown
                        isClearable={true}
                        onChange={(data: any) =>
                            handleDropdownChange('vessel', data)
                        }
                        vesselIdOptions={vesselIdOptions}
                    />
                </div>
            )}

            {!overdueList !== true && !excludeFilters.includes('trainer') && (
                <div>
                    <CrewDropdown
                        label=""
                        placeholder="Trainer"
                        isClearable={true}
                        multi
                        controlClasses="filter"
                        onChange={(data: any) =>
                            handleDropdownChange('trainer', data)
                        }
                        filterByTrainingSessionMemberId={memberId}
                        trainerIdOptions={trainerIdOptions}
                    />
                </div>
            )}

            {!excludeFilters.includes('crew') &&
                !excludeFilters.includes('member') && (
                    <div>
                        <CrewDropdown
                            isClearable={true}
                            label=""
                            multi
                            controlClasses="filter"
                            placeholder="Crew"
                            onChange={(data: any) =>
                                handleDropdownChange('member', data)
                            }
                            filterByTrainingSessionMemberId={memberId}
                            memberIdOptions={memberIdOptions}
                        />
                    </div>
                )}
        </div>
    )

    return (
        <>
            {bp.phablet ? (
                filterContent
            ) : (
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="maintenance-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            )}
        </>
    )
}

const TrainingCompletedReportFilter = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
}: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const [overdueList, setOverdueList] = useState(true)

    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    vesselIdOptions={vesselIdOptions}
                />
                <TrainingTypeDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('trainingType', data)
                    }
                    trainingTypeIdOptions={trainingTypeIdOptions}
                />
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    onChange={(data: any) =>
                        handleDropdownChange('trainer', data)
                    }
                    filterByTrainingSessionMemberId={memberId}
                    trainerIdOptions={trainerIdOptions}
                />
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                    filterByTrainingSessionMemberId={memberId}
                    memberIdOptions={memberIdOptions}
                />
            </div>
        </div>
    )
}

const CrewListFilter = ({ onChange }: any) => {
    const bp = useBreakpoints()
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const filterContent = (
        <div className="w-full grid grid-cols-4 tablet-sm:grid-cols-6 tablet-md:grid-cols-8 laptop:grid-cols-6 gap-2.5">
            <div className="flex col-span-4 tablet-sm:col-span-2 tablet-md:col-span-2 laptop:col-span-1">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
            </div>
            <div className="flex col-span-4 tablet-sm:col-span-2 tablet-md:col-span-2 laptop:col-span-1">
                <TrainingStatusDropdown
                    isClearable={true}
                    onChange={(data: any) => {
                        handleDropdownChange('trainingStatus', data)
                    }}
                />
            </div>
            <div className="flex col-span-4 tablet-sm:col-span-2 tablet-md:col-span-2 laptop:col-span-1">
                <CrewDutyDropdown
                    crewDutyID={0}
                    hideCreateOption
                    onChange={(data: any) => {
                        handleDropdownChange('crewDuty', data)
                    }}
                />
            </div>
            {bp['tablet-sm'] && (
                <div className="col-span-4 tablet-sm:col-span-6 tablet-md:col-span-2 laptop:col-end-7">
                    <SearchInput
                        className="lg:!w-full w-full"
                        onChange={(data: any) => {
                            handleDropdownChange('keyword', data)
                        }}
                    />
                </div>
            )}
        </div>
    )

    if (!bp['tablet-sm']) {
        return (
            <>
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="maintenance-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            </>
        )
    }

    return filterContent
}

const SearchInput = ({ onChange, className }: any) => {
    const debouncedOnChange = debounce(onChange, 600)

    const handleChange = (e: any) => {
        debouncedOnChange({ value: e.target.value })
    }
    return (
        <Input
            type="search"
            className={cn('h-[43px] w-full lg:w-[250px]', className)}
            placeholder="Search..."
            onChange={handleChange}
        />
    )
}

const DocumentLockerFilter = ({ onChange }: any) => {
    const bp = useBreakpoints()

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const filterContent = (
        <div className="w-full grid grid-cols-4 tablet-sm:grid-cols-6 tablet-md:grid-cols-8 laptop:grid-cols-6 gap-2.5">
            <div className="flex col-span-4 tablet-sm:col-span-3 tablet-md:col-span-2 laptop:col-span-1">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    classesName="min-w-52"
                />
            </div>
            <div className="flex col-span-4 tablet-sm:col-span-3 tablet-md:col-span-3 laptop:col-span-2">
                <DocumentModuleDropdown
                    onChange={(data: any) => {
                        handleDropdownChange('Module', data)
                    }}
                />
            </div>
            {bp['tablet-sm'] && (
                <div className="col-span-4 tablet-sm:col-span-6 tablet-md:col-span-2 tablet-md:col-end-9 laptop:col-end-7">
                    <SearchInput
                        className="lg:!w-full w-full"
                        onChange={(data: any) => {
                            handleDropdownChange('keyword', data)
                        }}
                    />
                </div>
            )}
        </div>
    )

    if (!bp['tablet-sm']) {
        return (
            <>
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="inventory-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            </>
        )
    }

    return filterContent
}

const InventoryListFilter = ({
    onChange,
    vesselIdOptions,
    supplierIdOptions,
    categoryIdOptions,
}: any) => {
    const bp = useBreakpoints()
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const filterContent = (
        <div className="w-full grid grid-cols-4 tablet-sm:grid-cols-6 tablet-md:grid-cols-8 laptop:grid-cols-6 gap-2.5">
            <div className="flex col-span-4 tablet-sm:col-span-2 tablet-md:col-span-2 laptop:col-span-1">
                <VesselDropdown
                    isClearable={true}
                    vesselIdOptions={vesselIdOptions}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
            </div>
            <div className="flex col-span-4 tablet-sm:col-span-2 tablet-md:col-span-2 laptop:col-span-1">
                <SupplierDropdown
                    isClearable={true}
                    supplierIdOptions={supplierIdOptions}
                    onChange={(data: any) =>
                        handleDropdownChange('supplier', data)
                    }
                />
            </div>
            <div className="flex col-span-4 tablet-sm:col-span-2 tablet-md:col-span-2 laptop:col-span-1">
                <CategoryDropdown
                    isClearable={true}
                    categoryIdOptions={categoryIdOptions}
                    onChange={(data: any) =>
                        handleDropdownChange('category', data)
                    }
                />
            </div>
            {bp['tablet-sm'] && (
                <div className="col-span-4 tablet-sm:col-span-6 tablet-md:col-span-2 laptop:col-end-7">
                    <SearchInput
                        className="lg:!w-full w-full"
                        onChange={(data: any) => {
                            handleDropdownChange('keyword', data)
                        }}
                    />
                </div>
            )}
        </div>
    )

    if (!bp['tablet-sm']) {
        return (
            <>
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="inventory-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            </>
        )
    }

    return filterContent
}

const SupplierListFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
        </div>
    )
}

const SearchInputOnlyFilter = ({ onChange }: any) => {
    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <SearchInput
                    onChange={(data: any) => {
                        onChange({ type: 'keyword', data })
                    }}
                />
            </div>
        </div>
    )
}

const MaintenanceListFilter = ({ onChange }: any) => {
    const bp = useBreakpoints()
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const filterContent = (
        <div className="grid tablet-sm:grid-cols-3 laptop:grid-cols-7 desktop:grid-cols-8 gap-2.5">
            <div className="     tablet-sm:col-span-1 laptop:col-span-2">
                <DateRange
                    className="border"
                    clearable
                    placeholder="Due Date Range"
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                />
            </div>

            <div className="flex tablet-sm:col-span-1">
                <MaintenanceStatusDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('status', data)
                    }
                />
            </div>

            <div className="flex tablet-sm:col-span-1">
                <MaintenanceCategoryDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('category', data)
                    }
                />
            </div>

            <div className="flex tablet-sm:col-span-1">
                <MaintenanceRecurringDropdown
                    onChange={(data: any) =>
                        handleDropdownChange('recurring', data)
                    }
                />
            </div>

            <div className="flex tablet-sm:col-span-1">
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                />
            </div>

            <div className="flex tablet-sm:col-span-1">
                <VesselDropdown
                    className="small:col-span-2 tablet-sm:col-span-3"
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
            </div>
            {bp['tablet-sm'] && (
                <div className="flex tablet-sm:col-span-3 laptop:col-span-2 laptop:col-end-8 desktop:col-span-1">
                    <SearchInput
                        className="lg:w-full"
                        onChange={(data: any) => {
                            handleDropdownChange('keyword', data)
                        }}
                    />
                </div>
            )}
        </div>
    )

    if (!bp['tablet-sm']) {
        return (
            <>
                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="maintenance-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            </>
        )
    }

    return filterContent
}

const MaintenanceStatusDropdown = ({ onChange }: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [selectedValue, setSelectedValue] = useState()
    const statusOptions = [
        { value: 'Open', label: 'Open' },
        { value: 'Save_As_Draft', label: 'Save as Draft' },
        { value: 'In_Progress', label: 'In Progress' },
        { value: 'On_Hold', label: 'On Hold' },
        { value: 'Overdue', label: 'Overdue' },
        { value: 'Completed', label: 'Completed' },
    ]

    useEffect(() => {
        setIsLoading(false)
    }, [])

    return (
        <>
            {statusOptions && !isLoading && (
                // <SLSelect
                //     id="supplier-dropdown"
                //     closeMenuOnSelect={true}
                //     options={statusOptions}
                //     menuPlacement="top"
                //     // defaultValue={selectedSupplier}
                //     // value={selectedSupplier}
                //     onChange={onChange}
                //     isClearable={true}
                //     placeholder="Status"
                // />

                <Combobox
                    options={statusOptions}
                    value={selectedValue}
                    onChange={(selectedOption: any) => {
                        setSelectedValue(selectedOption)
                        onChange(selectedOption)
                    }}
                    title="Status"
                    placeholder="Status"
                    /*''
                    classNames={{
                        control: () => classes.selectControl + ' !min-w-48',
                        singleValue: () => classes.selectSingleValue,
                        menu: () => classes.selectMenu,
                        option: () => '',
                    }}*/
                />
            )}
        </>
    )
}

const MaintenanceRecurringDropdown = ({ onChange }: any) => {
    const [selectedValue, setSelectedValue] = useState()

    // const recurringOptions = [
    //     { value: 'recurring', label: 'Recurring' },
    //     { value: 'one-off', label: 'One-off' },
    // ]
    const recurringOptions = [
        { value: 'Expiry', label: 'Due by date' },
        { value: 'EngineHours', label: 'Due by engine hours' },
        { value: 'Recurring', label: 'Recurring task' },
    ]

    const handleOnChange = (value: any) => {
        setSelectedValue(value)
        onChange(value)
    }

    return (
        <Combobox
            options={recurringOptions}
            value={selectedValue}
            onChange={handleOnChange}
            placeholder="Task Type"
        />
    )
}

const TrainingTypeListFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <div className="grid grid-cols-5 gap-2.5">
            <VesselDropdown
                isClearable={true}
                className="col-span-3 sm:col-span-2"
                onChange={(data: any) => handleDropdownChange('vessel', data)}
            />
            <div className="col-span-2 sm:col-span-1 col-end-6 sm:col-end-6">
                <SearchInput
                    className="!w-full"
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
        </div>
    )
}

const ReporingFilters = ({
    onChange,
    onClickButton,
    crewData,
    vesselData,
}: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const [crewIsMulti, setCrewIsMulti] = useState(true)
    const [vesselIsMulti, setVesselIsMulti] = useState(true)

    const getReport = () => {
        onClickButton()
    }

    useEffect(() => {
        if (crewData.length > 1) {
            setVesselIsMulti(false)
        } else {
            setVesselIsMulti(true)
        }

        if (vesselData.length > 1) {
            setCrewIsMulti(false)
        } else {
            setCrewIsMulti(true)
        }
    }, [crewData, vesselData])
    return (
        <div className="flex flex-col md:flex-row gap-2.5 w-full">
            <div className="mr-2">
                <DateRange
                    className="border "
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                />
            </div>
            <div className="mr-2">
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                    isMulti={crewIsMulti}
                />
            </div>
            <div className="mr-2">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    isMulti={vesselIsMulti}
                />
            </div>
            <div className="mr-2">
                <SeaLogsButton
                    text={'Report'}
                    type="primary"
                    color="sky"
                    action={getReport}
                />
            </div>
        </div>
    )
}

const FuelReporingFilters = ({ onChange }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-col md:flex-row gap-2.5 w-full">
            <div className="mr-2">
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    dateFormat="MMM do, yyyy"
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
            </div>
            <div className="mr-2">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
            </div>
        </div>
    )
}

const DocumentModuleDropdown = ({ onChange, multi = true, className }: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [selectedDocumentModule, setSelectedDocumentModule] = useState(
        [] as any,
    )

    const statusOptions = [
        { value: 'Vessel', label: 'Vessel' },
        { value: 'Maintenance', label: 'Maintenance' },
        { value: 'Inventory', label: 'Inventory' },
        { value: 'Company', label: 'Company' },
    ]

    useEffect(() => {
        setIsLoading(false)
    }, [])

    const handleOnChange = (selectedOption: any) => {
        setSelectedDocumentModule(selectedOption)
        onChange(selectedOption)
    }

    return (
        <>
            {statusOptions && !isLoading && (
                <Combobox
                    options={statusOptions}
                    value={selectedDocumentModule}
                    onChange={handleOnChange}
                    title="Module"
                    placeholder="Module"
                    multi={multi} // Enables multi-select when needed
                />
            )}
        </>
    )
}

const CalendarModuleDropdpown = ({ onChange }: any) => {
    const [isLoading, setIsLoading] = useState(true)

    const statusOptions = [
        { value: 'Task', label: 'Maintenance' },
        { value: 'Completed Training', label: 'Completed Training' },
        { value: 'Training Due', label: 'Training Due' },
        { value: 'Log Book Entry', label: 'Log Book Entry' },
    ]

    useEffect(() => {
        setIsLoading(false)
    }, [])

    return (
        <>
            {statusOptions && !isLoading && (
                <Combobox
                    id="document-module-dropdown"
                    options={statusOptions}
                    onChange={(element) => {
                        onChange('Module', element)
                    }}
                    className="max-w-[200px]"
                    placeholder="Module"
                />
            )}
        </>
    )
}

const CalendarFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    return (
        <Card>
            <CardContent className="flex gap-2.5">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                />

                <CalendarModuleDropdpown
                    onChange={(module: any, data: any) => {
                        handleDropdownChange('Module', data)
                    }}
                />
            </CardContent>
        </Card>
    )
}

const CrewSeatimeReportFilter = ({ onChange, onClickButton }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>()

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const bp = useBreakpoints()

    const getReport = () => {
        onClickButton()
    }

    const filterContent = (
        <div className="grid grid-cols-1 tablet-sm:grid-cols-4 tablet-lg:grid-cols-12 gap-2.5">
            <div className=" tablet-lg:col-span-3 laptop:col-span-3">
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    clearable
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
            </div>
            <div className="flex tablet-lg:col-span-3 laptop:col-span-3">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessels', data)
                    }
                    ilgulti={true}
                />
            </div>
            <div className="flex tablet-lg:col-span-3 laptop:col-span-2">
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) => {
                        handleDropdownChange('members', data)
                    }}
                    multi={true}
                />
            </div>
            <div className="flex tablet-lg:col-span-3 laptop:col-span-2">
                <CrewDutyDropdown
                    crewDutyID={0}
                    multi={true}
                    onChange={(data: any) => {
                        handleDropdownChange('crewDuty', data)
                    }}
                />
            </div>
            {bp['tablet-sm'] && (
                <div className="flex tablet-sm:col-span-4 tablet-lg:col-span-3 tablet-sm:col-end-5 tablet-lg:col-end-13 laptop:col-span-2">
                    <Button
                        type="button"
                        className="w-full"
                        onClick={getReport}>
                        Generate report
                    </Button>
                </div>
            )}
        </div>
    )

    if (!bp['tablet-sm']) {
        return (
            <>
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="crew-seatime-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
                <Button
                    type="button"
                    className="w-full"
                    iconLeft={CheckIcon}
                    onClick={getReport}>
                    Generate report
                </Button>
            </>
        )
    }

    return filterContent
}

const MultiVesselsDateRangeFilter = ({ onChange, onClickButton }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })

    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const getReport = () => {
        onClickButton()
    }

    return (
        <div className="grid grid-cols-1 tablet-sm:grid-cols-5 gap-2.5 w-full">
            <div className="tablet-sm:col-span-2">
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    dateFormat="MMM do, yyyy"
                    clearable
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
            </div>
            <div className="flex tablet-sm:col-span-2">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessels', data)
                    }
                    isMulti={true}
                />
            </div>

            <div className="tablet-sm:col-span-1">
                <Button className="w-full" onClick={getReport}>
                    Generate report
                </Button>
            </div>
        </div>
    )
}

const ActivityReportFilter = ({ onChange, onClickButton }: any) => {
    const [dateRange, setDaterange] = useState<TDateRange>({
        from: new Date(),
        to: new Date(),
    })
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }
    const [selectedValue, setSelectedValue] = useState()

    const getReport = () => {
        onClickButton()
    }

    return (
        <div className="flex flex-col md:flex-row gap-2.5 mt-2 w-full">
            <div>
                <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                    <div className="flex items-center">
                        {/*<Select
                            id="activity-report-type-dropdown"
                            closeMenuOnSelect={true}
                            options={activityReportTypes}
                            menuPlacement="top"
                            onChange={(data: any) =>
                                handleDropdownChange(
                                    'report_type',
                                    data?.value ?? '',
                                )
                            }
                            className={}
                            placeholder="Activity Report Type"
                            classNames={{
                                control: () =>
                                    classes.selectControl + ' w-full',
                            }}
                            styles={{
                                container: (provided) => ({
                                    ...provided,
                                    width: '100%',
                                }),
                            }}
                        />*/}
                    </div>
                </div>
            </div>
            <div className="flex flex-col md:flex-row gap-2.5 w-full">
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    dateFormat="MMM do, yyyy"
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessels', data)
                    }
                    isMulti={true}
                />
                <Button type="button" iconLeft={CheckIcon} onClick={getReport}>
                    Apply Filter
                </Button>
            </div>
        </div>
    )
}

const MaintenanceReportFilter = ({ onChange, onClickButton }: any) => {
    const bp = useBreakpoints()
    const [dateRange, setDaterange] = useState<TDateRange>()
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const getReport = () => {
        onClickButton()
    }

    const filterContent = (
        <div className="grid grid-cols-1 tablet-sm:grid-cols-4 tablet-lg:grid-cols-12 laptop:grid-cols-12 gap-2.5 w-full">
            <div className="tablet-sm:col-span-2 tablet-lg:col-span-3 laptop:col-span-2">
                <DateRange
                    type="date"
                    mode="range"
                    value={dateRange}
                    clearable
                    dateFormat="MMM do, yyyy"
                    onChange={(data: any) => {
                        setDaterange({
                            from: data?.startDate,
                            to: data?.endDate,
                        })
                        handleDropdownChange('dateRange', data)
                    }}
                />
            </div>
            <div className="flex tablet-sm:col-span-2 tablet-lg:col-span-3 laptop:col-span-2">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessels', data)
                    }
                    isMulti={true}
                />
            </div>
            <div className="flex tablet-sm:col-span-1 tablet-lg:col-span-2 laptop:col-span-2">
                <MaintenanceCategoryDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('category', data)
                    }
                />
            </div>
            <div className="flex tablet-sm:col-span-1 tablet-lg:col-span-2 laptop:col-span-2">
                <CrewDropdown
                    isClearable={true}
                    controlClasses="filter"
                    placeholder="Allocated Crew"
                    onChange={(data: any) =>
                        handleDropdownChange('member', data)
                    }
                />
            </div>

            <div className="flex tablet-sm:col-span-1 tablet-lg:col-span-2 laptop:col-span-2">
                <MaintenanceStatusDropdown
                    onChange={(data: any) => {
                        handleDropdownChange('status', data)
                    }}
                />
            </div>

            {bp['tablet-sm'] && (
                <div className="flex tablet-sm:col-span-1 tablet-lg:col-span-4 tablet-lg:col-end-13 laptop:col-span-2">
                    <Button className="w-full" onClick={getReport}>
                        Generate report
                    </Button>
                </div>
            )}
        </div>
    )

    if (!bp['tablet-sm']) {
        return (
            <>
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="maintenance-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
                <Button className="w-full" onClick={getReport}>
                    Generate report
                </Button>
            </>
        )
    }

    return filterContent
}

const TripReportFilters = ({
    tripReportFilterData,
    onChange,
    onClick,
}: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    const getReport = () => {
        onClick && onClick()
    }

    return (
        <div className="space-y-2.5">
            <div className="grid grid-cols-6 gap-2.5 w-full">
                <div className="">
                    <DateRange
                        onChange={(data: any) =>
                            handleDropdownChange('dateRange', data)
                        }
                        clearable
                    />
                </div>
                <div className="flex">
                    <LocationField
                        handleLocationChange={(value: any) => {
                            // If value is null or undefined, return early
                            if (!value) {
                                handleDropdownChange('fromLocation', null)
                                return
                            }

                            // Pass the value directly to handleDropdownChange
                            handleDropdownChange('fromLocation', value)
                        }}
                        setCurrentLocation={() => {}}
                        currentEvent={{}}
                        showAddNewLocation={false}
                        showUseCoordinates={false}
                        showCurrentLocation={false}
                    />
                </div>
                <div className="flex">
                    <LocationField
                        handleLocationChange={(value: any) => {
                            // If value is null or undefined, return early
                            if (!value) {
                                handleDropdownChange('toLocation', null)
                                return
                            }

                            // Pass the value directly to handleDropdownChange
                            handleDropdownChange('toLocation', value)
                        }}
                        setCurrentLocation={() => {}}
                        currentEvent={{}}
                        showAddNewLocation={false}
                        showUseCoordinates={false}
                        showCurrentLocation={false}
                    />
                </div>

                <div className="flex">
                    <VesselDropdown
                        isClearable
                        isMulti
                        onChange={(data: any) =>
                            handleDropdownChange('vessels', data)
                        }
                    />
                </div>
                <div className="flex col-span-2 gap-2.5">
                    <div className="flex items-center gap-2.5">
                        <Label htmlFor="from-time" position="left" label="From">
                            <TimeField
                                time={tripReportFilterData.fromTime ?? ''}
                                timeID="from-time"
                                fieldName="From"
                                buttonLabel="Set To Now"
                                hideButton={true}
                                handleTimeChange={(data: any) =>
                                    handleDropdownChange(
                                        'fromTime',
                                        dayjs(data).format('HH:mm'),
                                    )
                                }
                            />
                        </Label>
                        <Label htmlFor="to-time" position="left" label="To">
                            <TimeField
                                time={tripReportFilterData.toTime ?? ''}
                                timeID="to-time"
                                fieldName="To"
                                buttonLabel="Set To Now"
                                hideButton={true}
                                handleTimeChange={(data: any) =>
                                    handleDropdownChange(
                                        'toTime',
                                        dayjs(data).format('HH:mm'),
                                    )
                                }
                            />
                        </Label>
                    </div>
                    <CheckFieldLabel
                        id="trips-zero-pax"
                        type="checkbox"
                        className="w-full text-start"
                        checked={tripReportFilterData.noPax || false}
                        onCheckedChange={(checked) => {
                            handleDropdownChange('noPax', checked)
                        }}
                        label="Trips with Zero Pax"
                    />
                </div>

                {/* <div className="mr-2">
                <TripScheduleServiceDropdown
                    isClearable
                    isMulti
                    withNonScheduledOption
                    onChange={(data: any) =>
                        handleDropdownChange('tripScheduleServices', data)
                    }
                />
            </div> */}
            </div>
            <div className="flex justify-end">
                <Button onClick={getReport}>Generate report</Button>
            </div>
        </div>
    )
}

const AllocatedTasksFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-1 items-center justify-between">
            <div className="flex flex-col md:flex-row gap-2.5 flex-1 w-full">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />

                <MaintenanceStatusDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('status', data)
                    }
                />

                <SearchInput
                    onChange={(data: any) => {
                        handleDropdownChange('keyword', data)
                    }}
                />
            </div>
        </div>
    )
}

// Unified Training Filter - integrates the standalone UnifiedTrainingFilter into the main filter system
const UnifiedTrainingListFilter = ({
    onChange,
    vesselIdOptions = [],
    trainingTypeIdOptions = [],
    memberId = 0,
    trainerIdOptions = [],
    memberIdOptions = [],
}: any) => {
    // Category options for the combobox
    const categoryOptions = [
        {
            label: 'Status',
            value: 'all',
        },
        {
            label: 'Overdue',
            value: 'overdue',
        },
        {
            label: 'Upcoming',
            value: 'upcoming',
        },
        {
            label: 'Completed',
            value: 'completed',
        },
    ]

    const [selectedCategory, setSelectedCategory] = useState<any>(
        categoryOptions[0], // Default to "All Categories"
    )

    // Memoize the dropdown change handler to prevent unnecessary re-renders
    const handleDropdownChange = useCallback(
        (type: string, data: any) => {
            onChange({ type, data })
        },
        [onChange],
    )

    // Handle category selection
    const handleCategoryChange = useCallback(
        (selectedOption: any) => {
            setSelectedCategory(selectedOption)
            handleDropdownChange('category', selectedOption?.value || 'all')
        },
        [handleDropdownChange],
    )

    const bp = useBreakpoints()
    const getResponsiveDateFormat = () => {
        if (bp.laptop) {
            // Large screens (desktop): Full format
            return 'MMM do, yyyy' // e.g., "Jan 1st, 2024"
        } else if (bp['tablet-md']) {
            // Medium screens (tablet): Abbreviated format
            return 'MMM d, yyyy' // e.g., "Jan 1, 2024"
        } else {
            // Small screens (mobile): Compact format
            return 'M/d/yy' // e.g., "1/1/24"
        }
    }

    const filterContent = (
        <div className="grid tablet-sm:grid-cols-3 desktop:grid-cols-6 gap-2.5">
            <div className="     tablet-sm:col-span-1 desktop:col-span-1 ">
                <DateRange
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                    dateFormat="M/d/yy"
                    clearable
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <CrewDropdown
                    label=""
                    placeholder="Trainer"
                    isClearable={true}
                    multi
                    controlClasses="filter"
                    onChange={(data: any) => {
                        handleDropdownChange('trainer', data)
                    }}
                    filterByTrainingSessionMemberId={memberId}
                    trainerIdOptions={trainerIdOptions}
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <CrewDropdown
                    isClearable={true}
                    label=""
                    multi
                    controlClasses="filter"
                    placeholder="Crew"
                    onChange={(data: any) => {
                        handleDropdownChange('member', data)
                    }}
                    filterByTrainingSessionMemberId={memberId}
                    memberIdOptions={memberIdOptions}
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <Combobox
                    options={categoryOptions}
                    value={selectedCategory}
                    onChange={handleCategoryChange}
                    placeholder="All Categories"
                    buttonClassName="w-full"
                    searchThreshold={10} // Disable search for small list
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <VesselDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                    vesselIdOptions={vesselIdOptions}
                />
            </div>
            <div className="flex tablet-sm:col-span-1 desktop:col-span-1 ">
                <TrainingTypeDropdown
                    isClearable={true}
                    onChange={(data: any) =>
                        handleDropdownChange('trainingType', data)
                    }
                    trainingTypeIdOptions={trainingTypeIdOptions}
                />
            </div>
        </div>
    )

    return (
        <>
            {bp['tablet-sm'] ? (
                filterContent
            ) : (
                <Accordion type="single" collapsible className="w-full mt-2.5">
                    <AccordionItem value="unified-training-filters">
                        <AccordionTrigger>Filters</AccordionTrigger>
                        <AccordionContent>{filterContent}</AccordionContent>
                    </AccordionItem>
                </Accordion>
            )}
        </>
    )
}

const IncidentRecordFilter = ({ onChange }: any) => {
    const handleDropdownChange = (type: string, data: any) => {
        onChange({ type, data })
    }

    return (
        <div className="flex flex-col md:flex-row gap-2.5 w-full">
            <div className="flex w-full md:w-1/3">
                <VesselDropdown
                    isClearable={true}
                    placeholder="Filter by Vessel"
                    onChange={(data: any) =>
                        handleDropdownChange('vessel', data)
                    }
                />
            </div>
            <div className="w-full md:w-2/3">
                <DateRange
                    placeholder="Filter by Date Range"
                    clearable
                    onChange={(data: any) =>
                        handleDropdownChange('dateRange', data)
                    }
                />
            </div>
        </div>
    )
}
