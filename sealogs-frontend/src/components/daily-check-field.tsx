'use client'

import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
    InfoIcon,
    MessageSquare,
    Loader2,
    MessageSquareText,
} from 'lucide-react'
import { Textarea } from '@/components/ui/textarea'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { cn } from '@/app/lib/utils'
import { H4, P } from './ui/typography'
import {
    Sheet,
    SheetBody,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import UploadCloudFlareCaptures from '@/app/ui/logbook/components/upload-images'
import { Tooltip, TooltipContent, TooltipTrigger } from './ui'

// CheckField components following Shadcn UI pattern
const CheckField = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
    <div ref={ref} className={cn('h-fit bg-card', className)} {...props} />
))
CheckField.displayName = 'CheckField'

const CheckFieldHeader = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
    <div
        ref={ref}
        className={cn('flex flex-col space-y-1.5 pb-2.5', className)}
        {...props}
    />
))
CheckFieldHeader.displayName = 'CheckFieldHeader'

const CheckFieldTitle = React.forwardRef<
    HTMLHeadingElement,
    React.HTMLAttributes<HTMLHeadingElement>
>(({ className, children, ...props }, ref) => (
    <H4 ref={ref} className={cn('', className)} {...props}>
        {children}
    </H4>
))
CheckFieldTitle.displayName = 'CheckFieldTitle'

// New component for top container
const CheckFieldTopContent = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
    <div
        ref={ref}
        className={cn(
            'text-input font-medium border-b border-border flex justify-end',
            className,
        )}
        {...props}>
        <div className="flex w-12 h-10 rounded-t-md text-destructive justify-center items-center">
            No
        </div>
        <div className="flex w-12 h-10 rounded-t-md text-bright-turquoise-600 text justify-center items-center">
            Yes
        </div>
    </div>
))

CheckFieldTopContent.displayName = 'CheckFieldTopContent'

const CheckFieldContent = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
    <div ref={ref} className={cn('pt-0', className)} {...props} />
))
CheckFieldContent.displayName = 'CheckFieldContent'

const CheckFieldFooter = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
    <div
        ref={ref}
        className={cn('flex items-center p-4 pt-0', className)}
        {...props}
    />
))
CheckFieldFooter.displayName = 'CheckFieldFooter'

interface DailyCheckFieldProps {
    locked?: boolean
    displayField?: boolean
    displayLabel?: string
    displayDescription?: string | React.ReactNode
    descriptionType?: string // Kept for backward compatibility
    inputId: string
    handleNoChange: (id?: string) => void
    handleYesChange: (id?: string) => void
    defaultNoChecked?: boolean
    defaultYesChecked?: boolean
    comment?: string
    // These props are kept for backward compatibility but are no longer required
    setDescriptionPanelContent?: (content: string | React.ReactNode) => void
    setOpenDescriptionPanel?: (open: boolean) => void
    setDescriptionPanelHeading?: (heading: string) => void
    className?: string
    innerWrapperClassName?: string
    onCommentSave?: (comment: string) => void | Promise<void>
    onCommentDelete?: () => void | Promise<void>
    commentAction?: () => void
    offline?: boolean // Used in parent components for offline functionality
    fieldId?: string
    onError?: (error: Error) => void
    disabled?: boolean
    required?: boolean
    hideCommentButton?: boolean
    displayImage?: boolean // New prop to control image upload functionality
    fieldImages?: object | boolean
    onImageUpload?: () => void | Promise<void>
    sectionData?: { id: number; sectionName: string }
}

export function DailyCheckField({
    locked = false,
    displayField = true,
    displayLabel = '',
    displayDescription = '',
    descriptionType, // Kept for backward compatibility
    inputId,
    handleNoChange,
    handleYesChange,
    defaultNoChecked = false,
    defaultYesChecked = false,
    comment = '',
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    setDescriptionPanelHeading,
    className = '',
    innerWrapperClassName = '',
    onCommentSave,
    onCommentDelete,
    commentAction,
    offline = false, // Used in parent components for offline functionality
    fieldId,
    onError,
    disabled = false,
    required = false,
    hideCommentButton = false,
    displayImage = false, // New prop to control image upload functionality
    fieldImages = false, // Kept for backward compatibility
    onImageUpload, // Callback for image upload
    sectionData = { id: 0, sectionName: 'logBookEntryID' },
    ...props
}: DailyCheckFieldProps) {
    const [yes, setYes] = useState(defaultYesChecked)
    const [no, setNo] = useState(defaultNoChecked)
    const [openCommentDialog, setOpenCommentDialog] = useState(false)
    const [currentComment, setCurrentComment] = useState(comment)
    const [commentText, setCommentText] = useState(comment)
    const [isSaving, setIsSaving] = useState(false)
    const [hasError, setHasError] = useState(false)
    const isInitialRender = useRef(true)
    const isDisabled = locked || disabled

    // Internal state for description panel
    const [internalOpenDescriptionPanel, setInternalOpenDescriptionPanel] =
        useState(false)
    const [
        internalDescriptionPanelContent,
        setInternalDescriptionPanelContent,
    ] = useState<string | React.ReactNode>('')
    const [
        internalDescriptionPanelHeading,
        setInternalDescriptionPanelHeading,
    ] = useState('')

    // Handle Yes selection
    function callYes() {
        if (isDisabled) return

        try {
            handleYesChange(fieldId)
            setYes(true)
            setNo(false)
        } catch (error) {
            console.error('Error handling Yes change:', error)
            if (onError && error instanceof Error) {
                onError(error)
            }
        }
    }

    // Handle No selection
    function callNo() {
        if (isDisabled) return

        try {
            handleNoChange(fieldId)
            setNo(true)
            setYes(false)
        } catch (error) {
            console.error('Error handling No change:', error)
            if (onError && error instanceof Error) {
                onError(error)
            }
        }
    }

    // Handle opening the comment dialog
    const handleOpenCommentDialog = () => {
        // if (isDisabled) return
        if (commentAction) {
            commentAction()
        } else {
            setOpenCommentDialog(true)
        }
    }

    // Handle saving comments with offline support
    const handleSaveComment = async () => {
        if (isDisabled) return

        setIsSaving(true)
        setHasError(false)

        try {
            setCurrentComment(commentText)

            if (onCommentSave) {
                // Handle both synchronous and asynchronous onCommentSave
                const result = onCommentSave(commentText)
                if (result instanceof Promise) {
                    await result
                }
            }

            setOpenCommentDialog(false)
        } catch (error) {
            console.error('Error saving comment:', error)
            setHasError(true)
            if (onError && error instanceof Error) {
                onError(error)
            }
        } finally {
            setIsSaving(false)
        }
    }

    // Handle deleting comments
    const handleDeleteComment = async () => {
        if (isDisabled) return

        setIsSaving(true)
        setHasError(false)

        try {
            // If onCommentDelete is provided, call it
            if (onCommentDelete) {
                try {
                    // Handle both synchronous and asynchronous onCommentDelete
                    const result = onCommentDelete()
                    if (result instanceof Promise) {
                        await result
                    }
                } catch (error) {
                    console.error('Error in onCommentDelete callback:', error)
                    // Continue with local deletion even if the callback fails
                }
            }

            // Always clear the comment locally
            setCurrentComment('')
            setCommentText('')
            setOpenCommentDialog(false)
        } catch (error) {
            console.error('Error deleting comment:', error)
            setHasError(true)
            if (onError && error instanceof Error) {
                onError(error)
            }
        } finally {
            setIsSaving(false)
        }
    }

    // Update comment state when the comment prop changes
    useEffect(() => {
        if (!isInitialRender.current) {
            setCommentText(comment)
            setCurrentComment(comment)
        }
        isInitialRender.current = false
    }, [comment])

    // Update yes/no state when defaultChecked props change
    useEffect(() => {
        setYes(defaultYesChecked)
        setNo(defaultNoChecked)
    }, [defaultYesChecked, defaultNoChecked])

    // If displayField is false, don't render the component at all
    if (!displayField) {
        return null
    }

    const renderRadioOptions = () => (
        <RadioGroup
            {...props}
            variant="horizontal"
            className={cn({
                'opacity-60': isDisabled,
            })}
            gap={'none'}
            value={yes ? 'yes' : no ? 'no' : ''}
            onValueChange={(value) => {
                if (isDisabled) return
                if (value === 'yes') {
                    callYes()
                } else if (value === 'no') {
                    callNo()
                }
            }}
            disabled={isDisabled}>
            {/* No Option - Destructive */}
            <div
                className={cn(
                    'flex w-12 bg-destructive-foreground justify-center phablet:p-0 items-center',
                )}>
                <RadioGroupItem
                    value="no"
                    id={`${inputId}-no_radio`}
                    variant="destructive"
                    size="lg"
                />
            </div>

            {/* Yes Option - Success */}
            <div
                className={cn(
                    'flex w-12 bg-bright-turquoise-100 justify-center items-center ',
                )}>
                <RadioGroupItem
                    value="yes"
                    id={`${inputId}-yes_radio`}
                    variant="success"
                    size="lg"
                />
            </div>
        </RadioGroup>
    )

    const getFile = () => {
        if (fieldImages && Array.isArray(fieldImages)) {
            return fieldImages
                .filter((image: any) => image.fieldName === inputId)
                .sort((a: any, b: any) => b.id - a.id)
        }
        return false
    }

    const renderActionButtons = () => (
        <div className="flex items-center gap-5 py-2.5">
            {displayImage && (
                <UploadCloudFlareCaptures
                    file={getFile()}
                    setFile={onImageUpload}
                    inputId={inputId}
                    buttonType="icon"
                    sectionData={sectionData}
                />
            )}
            {/* Description Button */}
            {displayDescription && (
                <Button
                    variant="ghost"
                    size="icon"
                    iconOnly
                    title="View description"
                    iconLeft={
                        <InfoIcon
                            className="text-light-blue-vivid-900 fill-light-blue-vivid-50"
                            size={24}
                        />
                    }
                    onClick={() => {
                        // Use external state handlers if provided (for backward compatibility)
                        if (
                            setDescriptionPanelContent &&
                            setOpenDescriptionPanel &&
                            setDescriptionPanelHeading
                        ) {
                            setDescriptionPanelContent(displayDescription)
                            setOpenDescriptionPanel(true)
                            setDescriptionPanelHeading(displayLabel)
                        }
                        // Always use internal state
                        setInternalDescriptionPanelContent(displayDescription)
                        setInternalOpenDescriptionPanel(true)
                        setInternalDescriptionPanelHeading(displayLabel)
                    }}
                />
            )}
            {/* Comment Button */}
            {!hideCommentButton && (
                <Button
                    variant="ghost"
                    size="icon"
                    iconOnly
                    title="Add comment"
                    className="group"
                    iconLeft={
                        <Tooltip>
                            <TooltipTrigger className='p-0'>
                                {currentComment ? (
                                    <MessageSquareText
                                        className={cn('text-curious-blue-400')}
                                        size={24}
                                    />
                                ) : (
                                    <MessageSquare
                                        className={cn(
                                            'text-outer-space-400 group-hover:text-outer-space-600',
                                            'will-change-transform will-change-width will-change-padding transform-gpu',
                                            'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                                        )}
                                        size={24}
                                    />
                                )}
                            </TooltipTrigger>
                            <TooltipContent>
                                {currentComment
                                    ? 'Edit comment'
                                    : 'Add comment'}
                            </TooltipContent>
                        </Tooltip>
                    }
                    onClick={handleOpenCommentDialog}
                    // disabled={isDisabled}
                />
            )}
        </div>
    )

    return (
        <>
            <div
                className={cn(
                    'flex gap-2.5 min-h-16 !mt-0 border-b border-border ',
                    innerWrapperClassName,
                )}>
                {/* Field Label */}
                <div className="flex-1 flex flex-col phablet:flex-row pt-2.5 phablet:pt-0 phablet:gap-2.5 items-end phablet:items-center">
                    <P className="text-card-foreground text-base w-full">
                        {displayLabel}
                        {required && (
                            <span className="text-destructive ml-1">*</span>
                        )}
                    </P>
                    {/* Action Buttons */}
                    {renderActionButtons()}
                </div>

                {/* Radio Options */}
                {renderRadioOptions()}
            </div>

            {/* Description Panel Sheet */}
            <Sheet
                open={internalOpenDescriptionPanel}
                onOpenChange={setInternalOpenDescriptionPanel}>
                <SheetContent
                    side="left"
                    className="w-[90%] sm:w-[540px] max-w-2xl"
                    onInteractOutside={() => {
                        setInternalOpenDescriptionPanel(false)
                        setInternalDescriptionPanelContent('')
                        setInternalDescriptionPanelHeading('')
                    }}>
                    <SheetHeader>
                        <SheetTitle>
                            {internalDescriptionPanelHeading}
                        </SheetTitle>
                    </SheetHeader>
                    <SheetBody>
                        {typeof internalDescriptionPanelContent === 'string' ? (
                            <div
                                className="prose prose-sm max-w-none leading-7"
                                dangerouslySetInnerHTML={{
                                    __html: internalDescriptionPanelContent as string,
                                }}
                            />
                        ) : (
                            <div className="prose prose-sm max-w-none leading-7">
                                {internalDescriptionPanelContent}
                            </div>
                        )}
                    </SheetBody>
                </SheetContent>
            </Sheet>

            {/* Comment Dialog */}
            <AlertDialogNew
                openDialog={openCommentDialog}
                setOpenDialog={setOpenCommentDialog}
                title={currentComment ? 'Edit comment' : 'Add comment'}
                handleCreate={!isDisabled ? handleSaveComment : undefined}
                handleDestructiveAction={
                    !isDisabled && !!currentComment
                        ? handleDeleteComment
                        : undefined
                }
                showDestructiveAction={!isDisabled && !!currentComment}
                actionText={isSaving ? 'Saving...' : 'Save'}
                destructiveActionText="Delete"
                destructiveLoading={isSaving}
                noButton={isDisabled}
                cancelText={isDisabled ? 'Close' : 'Cancel'}
                loading={isSaving}>
                <div className="flex flex-col">
                    {hasError && (
                        <div className="text-destructive mb-2 text-sm">
                            Error {currentComment ? 'updating' : 'saving'}{' '}
                            comment. Please try again.
                        </div>
                    )}
                    <Textarea
                        id={`${inputId}-comment`}
                        // readOnly={isDisabled}
                        disabled={isDisabled || isSaving}
                        rows={4}
                        autoResize
                        placeholder="Comment"
                        value={commentText}
                        onChange={(e) => setCommentText(e.target.value)}
                        className={cn('max-h-[60svh]', {
                            'border-destructive': hasError,
                        })}
                    />
                    {isSaving && (
                        <div className="flex items-center justify-center mt-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span className="text-sm">Saving...</span>
                        </div>
                    )}
                </div>
            </AlertDialogNew>
        </>
    )
}

// Export the CheckField components
export {
    CheckField,
    CheckFieldHeader,
    CheckFieldTitle,
    CheckFieldTopContent,
    CheckFieldContent,
    CheckFieldFooter,
}
