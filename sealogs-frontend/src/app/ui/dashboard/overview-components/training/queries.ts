import gql from 'graphql-tag'

export const ReadTrainingSessionDues = gql`
    query ReadTrainingSessionDues(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionDueFilterFields = {}
    ) {
        readTrainingSessionDues(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: {
                dueDate: ASC
                trainingTypeID: ASC
                vesselID: ASC
                memberID: ASC
            }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                dueDate
                vesselID
                vessel {
                    id
                    title
                }
                trainingTypeID
                trainingType {
                    id
                    title
                }
                member {
                    id
                    firstName
                    surname
                }
                trainingSession {
                    trainingLocationType
                }
            }
        }
    }
`

export const ReadTrainingSessions = gql`
    query ReadTrainingSessions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionFilterFields = {}
    ) {
        readTrainingSessions(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                date
            }
        }
    }
`
