'use client'

import { useRouter } from 'next/navigation'
import {
    Users,
    Fuel,
    Activity,
    Clock,
    Wrench,
    DollarSign,
    Gauge,
    ListChecks,
    FileText,
    Settings,
    Navigation,
    Droplet,
} from 'lucide-react'
import { Button, H1, H3 } from '@/components/ui'
import Link from 'next/link'

const reports = [
    {
        name: 'Crew Seatime Report',
        icon: <Users />,
        url: '/reporting/crew-seatime-report',
    },
    {
        name: 'Simple Fuel Report',
        icon: <Fuel />, // or <Droplet />
        url: '/reporting/simple-fuel-report',
    },
    {
        name: 'Activity Report',
        icon: <Activity />,
        url: '/reporting/activity-reports',
    },
    {
        name: 'Engine Hours Report',
        icon: <Clock />,
        url: '/reporting/engine-hours-report',
    },
    {
        name: 'Maintenance Status and Activity Report',
        icon: <Wrench />,
        url: '/reporting/maintenance-status-activity',
    },
    {
        name: 'Maintenance Cost Track Report',
        icon: <DollarSign />,
        url: '/reporting/maintenance-cost-track',
    },
    {
        name: 'Fuel Analysis Report',
        icon: <Gauge />,
        url: '/reporting/fuel-analysis',
    },
    {
        name: 'Fuel Tasking Analysis Report',
        icon: <ListChecks />,
        url: '/reporting/fuel-tasking-analysis',
    },
    {
        name: 'Detailed Fuel Report',
        icon: <FileText />,
        url: '/reporting/detailed-fuel-report',
    },
    {
        name: 'Summary Fuel Report',
        icon: <FileText />,
        url: '/reporting/fuel-summary-report',
    },
    {
        name: 'Service Report',
        icon: <Settings />,
        url: '/reporting/service-report',
    },
    {
        name: 'Trip Report',
        icon: <Navigation />,
        url: '/reporting/trip-report',
    },
    {
        name: 'Completed Trainings / Drills',
        icon: <Users />,
        url: '/reporting/crew-training-completed-report',
    },
]

export default function MainReporting() {
    const router = useRouter()
    // const [rescueVessel, setHasRescueVessel] = useState(false)

    // userHasRescueVessel(setHasRescueVessel)

    return (
        <div className="w-full grid gap-8 mt-8">
            <H1>Reporting</H1>
            <div className="flex flex-wrap gap-2">
                {reports.map((report) => {
                    return (
                        <Link key={report.name} href={report.url}>
                            <Button
                                iconLeft={report.icon}
                                variant="primaryOutline">
                                {report.name}
                            </Button>
                        </Link>
                    )
                })}
            </div>
        </div>
    )
}
